APP_NAME=Laravel
APP_LOGO_URL=https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/sederly-logo_ixqhqr.png
APP_LOGO_MAX_HEIGHT=60px
APP_LOGO_MAX_WIDTH=150px

# Authentication Pages Logo Configuration
AUTH_LOGO_URL=https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/sederly-logo_ixqhqr.png
AUTH_LOGO_MAX_HEIGHT=70px
AUTH_LOGO_MAX_WIDTH=200px

# Landing Page Logo Configuration
LANDING_LOGO_URL=https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/sederly-logo_ixqhqr.png
LANDING_LOGO_MAX_HEIGHT=40px
LANDING_LOGO_MAX_WIDTH=150px
LANDING_SHOW_TEXT_NAVBAR=true
LANDING_SHOW_TEXT_FOOTER=true
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"
