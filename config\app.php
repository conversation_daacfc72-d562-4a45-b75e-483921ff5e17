<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application, which will be used when the
    | framework needs to place the application's name in a notification or
    | other UI elements where an application name needs to be displayed.
    |
    */

    'name' => env('APP_NAME', 'Laravel'),

    /*
    |--------------------------------------------------------------------------
    | Application Logo URL
    |--------------------------------------------------------------------------
    |
    | This value is the URL of your application's logo, which will be used
    | in sidebars and other UI elements where the logo needs to be displayed.
    |
    */

    'logo_url' => env('APP_LOGO_URL', 'https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/sederly-logo_ixqhqr.png'),

    /*
    |--------------------------------------------------------------------------
    | Application Logo Size
    |--------------------------------------------------------------------------
    |
    | These values control the maximum dimensions of your application's logo
    | in the sidebar and other UI elements. Values should include CSS units.
    |
    */

    'logo_max_height' => env('APP_LOGO_MAX_HEIGHT', '60px'),
    'logo_max_width' => env('APP_LOGO_MAX_WIDTH', '150px'),

    /*
    |--------------------------------------------------------------------------
    | Authentication Pages Logo Configuration
    |--------------------------------------------------------------------------
    |
    | These values control the logo displayed on authentication pages (login,
    | registration, password reset, etc.). This allows for different branding
    | on auth pages versus the main application sidebar.
    |
    */

    'auth_logo_url' => env('AUTH_LOGO_URL', env('APP_LOGO_URL', 'https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/sederly-logo_ixqhqr.png')),
    'auth_logo_max_height' => env('AUTH_LOGO_MAX_HEIGHT', '70px'),
    'auth_logo_max_width' => env('AUTH_LOGO_MAX_WIDTH', '200px'),

    /*
    |--------------------------------------------------------------------------
    | Landing Page Logo Configuration
    |--------------------------------------------------------------------------
    |
    | These values control the logo displayed in the landing page navbar.
    | This allows for different branding on the public landing page versus
    | the authenticated application areas.
    |
    */

    'landing_logo_url' => env('LANDING_LOGO_URL', env('APP_LOGO_URL', 'https://res.cloudinary.com/dnyl8mkez/image/upload/v1753291187/sederly-logo_ixqhqr.png')),
    'landing_logo_max_height' => env('LANDING_LOGO_MAX_HEIGHT', '40px'),
    'landing_logo_max_width' => env('LANDING_LOGO_MAX_WIDTH', '150px'),
    'landing_show_text_navbar' => env('LANDING_SHOW_TEXT_NAVBAR', true),
    'landing_show_text_footer' => env('LANDING_SHOW_TEXT_FOOTER', true),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | the application so that it's available within Artisan commands.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. The timezone
    | is set to "UTC" by default as it is suitable for most use cases.
    |
    */

    'timezone' => env('APP_TIMEZONE', 'UTC'),

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by Laravel's translation / localization methods. This option can be
    | set to any locale for which you plan to have translation strings.
    |
    */

    'locale' => env('APP_LOCALE', 'en'),

    'fallback_locale' => env('APP_FALLBACK_LOCALE', 'en'),

    'faker_locale' => env('APP_FAKER_LOCALE', 'en_US'),

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is utilized by Laravel's encryption services and should be set
    | to a random, 32 character string to ensure that all encrypted values
    | are secure. You should do this prior to deploying the application.
    |
    */

    'cipher' => 'AES-256-CBC',

    'key' => env('APP_KEY'),

    'previous_keys' => [
        ...array_filter(
            explode(',', env('APP_PREVIOUS_KEYS', ''))
        ),
    ],

    /*
    |--------------------------------------------------------------------------
    | Maintenance Mode Driver
    |--------------------------------------------------------------------------
    |
    | These configuration options determine the driver used to determine and
    | manage Laravel's "maintenance mode" status. The "cache" driver will
    | allow maintenance mode to be controlled across multiple machines.
    |
    | Supported drivers: "file", "cache"
    |
    */

    'maintenance' => [
        'driver' => env('APP_MAINTENANCE_DRIVER', 'file'),
        'store' => env('APP_MAINTENANCE_STORE', 'database'),
    ],

];
