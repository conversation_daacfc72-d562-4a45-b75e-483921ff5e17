<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AffiliateSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'default_commission_rate',
        'minimum_withdrawal',
        'payment_methods',
        'terms_and_conditions',
        'auto_approve_earnings',
        'auto_approve_affiliates',
        'cookie_duration_days',
        'withdrawal_fee_percentage',
        'withdrawal_fee_fixed',
        'program_active',
        'welcome_message',
        'commission_tiers',
        'recurring_commissions',
        'max_referrals_per_affiliate',
    ];

    protected $casts = [
        'default_commission_rate' => 'decimal:2',
        'minimum_withdrawal' => 'decimal:2',
        'payment_methods' => 'array',
        'auto_approve_earnings' => 'boolean',
        'auto_approve_affiliates' => 'boolean',
        'withdrawal_fee_percentage' => 'decimal:2',
        'withdrawal_fee_fixed' => 'decimal:2',
        'program_active' => 'boolean',
        'commission_tiers' => 'array',
        'recurring_commissions' => 'boolean',
    ];

    /**
     * Get the singleton instance of affiliate settings
     */
    public static function getInstance(): self
    {
        $settings = static::first();
        
        if (!$settings) {
            $settings = static::create([
                'default_commission_rate' => 10.00,
                'minimum_withdrawal' => 50.00,
                'payment_methods' => ['bank_transfer', 'paypal'],
                'auto_approve_earnings' => false,
                'auto_approve_affiliates' => false,
                'cookie_duration_days' => 30,
                'withdrawal_fee_percentage' => 0.00,
                'withdrawal_fee_fixed' => 0.00,
                'program_active' => true,
                'recurring_commissions' => false,
            ]);
        }
        
        return $settings;
    }

    /**
     * Get available payment methods
     */
    public function getAvailablePaymentMethods(): array
    {
        return $this->payment_methods ?? ['bank_transfer', 'paypal'];
    }

    /**
     * Check if program is active
     */
    public function isProgramActive(): bool
    {
        return $this->program_active;
    }

    /**
     * Check if earnings should be auto-approved
     */
    public function shouldAutoApproveEarnings(): bool
    {
        return $this->auto_approve_earnings;
    }

    /**
     * Check if affiliates should be auto-approved
     */
    public function shouldAutoApproveAffiliates(): bool
    {
        return $this->auto_approve_affiliates;
    }

    /**
     * Calculate withdrawal fee
     */
    public function calculateWithdrawalFee(float $amount): float
    {
        $percentageFee = ($amount * $this->withdrawal_fee_percentage) / 100;
        $totalFee = $percentageFee + $this->withdrawal_fee_fixed;
        
        return round($totalFee, 2);
    }

    /**
     * Get commission rate for affiliate (considering tiers)
     */
    public function getCommissionRateForAffiliate(Affiliate $affiliate): float
    {
        if (!$this->commission_tiers) {
            return $this->default_commission_rate;
        }

        $referralCount = $affiliate->converted_referrals_count;
        
        // Sort tiers by referral count descending
        $tiers = collect($this->commission_tiers)->sortByDesc('min_referrals');
        
        foreach ($tiers as $tier) {
            if ($referralCount >= $tier['min_referrals']) {
                return $tier['commission_rate'];
            }
        }
        
        return $this->default_commission_rate;
    }

    /**
     * Check if recurring commissions are enabled
     */
    public function hasRecurringCommissions(): bool
    {
        return $this->recurring_commissions;
    }

    /**
     * Get cookie duration in days
     */
    public function getCookieDuration(): int
    {
        return $this->cookie_duration_days ?? 30;
    }

    /**
     * Check if affiliate can make more referrals
     */
    public function canMakeMoreReferrals(Affiliate $affiliate): bool
    {
        if (!$this->max_referrals_per_affiliate) {
            return true;
        }
        
        return $affiliate->referrals()->count() < $this->max_referrals_per_affiliate;
    }

    /**
     * Get formatted minimum withdrawal
     */
    public function getFormattedMinimumWithdrawalAttribute(): string
    {
        return format_price($this->minimum_withdrawal);
    }

    /**
     * Get formatted default commission rate
     */
    public function getFormattedCommissionRateAttribute(): string
    {
        return number_format($this->default_commission_rate, 2) . '%';
    }

    /**
     * Update setting value
     */
    public function updateSetting(string $key, $value): bool
    {
        if (in_array($key, $this->fillable)) {
            $this->$key = $value;
            return $this->save();
        }
        
        return false;
    }

    /**
     * Get all payment method options
     */
    public static function getAllPaymentMethods(): array
    {
        return [
            'bank_transfer' => 'Bank Transfer',
            'paypal' => 'PayPal',
            'stripe' => 'Stripe',
            'manual' => 'Manual Payment',
        ];
    }

    /**
     * Validate commission tiers structure
     */
    public function validateCommissionTiers(array $tiers): bool
    {
        foreach ($tiers as $tier) {
            if (!isset($tier['min_referrals']) || !isset($tier['commission_rate'])) {
                return false;
            }

            if (!is_numeric($tier['min_referrals']) || !is_numeric($tier['commission_rate'])) {
                return false;
            }

            if ($tier['min_referrals'] < 0 || $tier['commission_rate'] < 0) {
                return false;
            }
        }

        return true;
    }

    /**
     * Update commission rate for all affiliates with the old default rate
     */
    public function updateAffiliateCommissionRates(float $oldRate, float $newRate): int
    {
        return Affiliate::where('commission_rate', $oldRate)
            ->update(['commission_rate' => $newRate]);
    }

    /**
     * Get count of affiliates who would be affected by rate change
     */
    public function getAffectedAffiliatesCount(float $currentRate): int
    {
        return Affiliate::where('commission_rate', $currentRate)->count();
    }

    /**
     * Get system-wide affiliate statistics
     */
    public function getSystemStats(): array
    {
        return [
            'total_affiliates' => Affiliate::count(),
            'active_affiliates' => Affiliate::where('status', Affiliate::STATUS_ACTIVE)->count(),
            'pending_affiliates' => Affiliate::where('status', Affiliate::STATUS_PENDING)->count(),
            'suspended_affiliates' => Affiliate::where('status', Affiliate::STATUS_SUSPENDED)->count(),
            'total_referrals' => \App\Models\AffiliateReferral::count(),
            'converted_referrals' => \App\Models\AffiliateReferral::where('status', 'converted')->count(),
            'total_earnings' => \App\Models\AffiliateEarning::sum('amount'),
            'pending_earnings' => \App\Models\AffiliateEarning::where('status', 'pending')->sum('amount'),
            'approved_earnings' => \App\Models\AffiliateEarning::where('status', 'approved')->sum('amount'),
            'total_withdrawals' => \App\Models\AffiliateWithdrawal::sum('amount'),
            'pending_withdrawals' => \App\Models\AffiliateWithdrawal::where('status', 'pending')->sum('amount'),
            'paid_withdrawals' => \App\Models\AffiliateWithdrawal::where('status', 'paid')->sum('amount'),
        ];
    }
}
