<?php

/**
 * Test script to verify currency conversion fix
 * 
 * This script simulates the order creation process to verify that:
 * 1. User enters amount in NGN (e.g., 40,000 NGN)
 * 2. System converts to USD for storage (40,000 ÷ 1500 = $26.67)
 * 3. System displays converted amount back to NGN (26.67 × 1500 = 40,000 NGN)
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\CurrencyService;
use App\Models\CurrencyRate;
use App\Models\Currency;
use App\Models\CurrencySetting;

echo "=== Currency Conversion Test ===\n\n";

try {
    // Test 1: Check if currency rates exist
    echo "1. Checking currency rates...\n";
    $usdToNgn = CurrencyRate::where('from_currency', 'USD')
                           ->where('to_currency', 'NGN')
                           ->where('is_active', true)
                           ->first();
    
    $ngnToUsd = CurrencyRate::where('from_currency', 'NGN')
                           ->where('to_currency', 'USD')
                           ->where('is_active', true)
                           ->first();
    
    if ($usdToNgn) {
        echo "   ✓ USD to NGN rate: {$usdToNgn->rate}\n";
    } else {
        echo "   ✗ USD to NGN rate not found\n";
    }
    
    if ($ngnToUsd) {
        echo "   ✓ NGN to USD rate: {$ngnToUsd->rate}\n";
    } else {
        echo "   ✗ NGN to USD rate not found\n";
    }
    
    // Test 2: Test currency service conversion
    echo "\n2. Testing currency service conversion...\n";
    $currencyService = app(CurrencyService::class);
    
    // Simulate user entering 40,000 NGN
    $userAmount = 40000; // NGN
    $userCurrency = 'NGN';
    $baseCurrency = 'USD';
    
    echo "   User enters: {$userAmount} {$userCurrency}\n";
    
    // Convert to base currency (what should happen during order creation)
    $convertedToBase = $currencyService->convertToBase($userAmount, $userCurrency);
    echo "   Converted to base currency: {$convertedToBase} {$baseCurrency}\n";
    
    // Convert back to user currency (what should happen during display)
    $convertedBackToUser = $currencyService->convertFromBase($convertedToBase, $userCurrency);
    echo "   Converted back to user currency: {$convertedBackToUser} {$userCurrency}\n";
    
    // Test 3: Test helper functions
    echo "\n3. Testing helper functions...\n";
    
    // Simulate setting user currency to NGN
    session(['user_currency_web' => 'NGN']);
    $currencyService->setCurrentCurrency('NGN');
    
    echo "   Current user currency: " . user_currency() . "\n";
    echo "   Current currency symbol: " . user_currency_symbol() . "\n";
    echo "   Base currency: " . base_currency() . "\n";
    
    // Test format_price function
    $storedAmount = $convertedToBase; // This is what would be stored in database
    $formattedPrice = format_price($storedAmount);
    echo "   Formatted price (stored {$storedAmount} USD): {$formattedPrice}\n";
    
    // Test 4: Verify the fix
    echo "\n4. Verification Summary:\n";
    $tolerance = 0.01; // Allow small rounding differences
    
    if (abs($convertedBackToUser - $userAmount) < $tolerance) {
        echo "   ✓ PASS: Round-trip conversion is accurate\n";
        echo "   ✓ Original: {$userAmount} NGN\n";
        echo "   ✓ After conversion: {$convertedBackToUser} NGN\n";
        echo "   ✓ Difference: " . abs($convertedBackToUser - $userAmount) . " NGN\n";
    } else {
        echo "   ✗ FAIL: Round-trip conversion has significant error\n";
        echo "   ✗ Original: {$userAmount} NGN\n";
        echo "   ✗ After conversion: {$convertedBackToUser} NGN\n";
        echo "   ✗ Difference: " . abs($convertedBackToUser - $userAmount) . " NGN\n";
    }
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
