<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Super Admin') - {{ config('app.name', 'Sederly') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
        }
        .sidebar .nav-link {
            color: #ecf0f1;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0;
        }
        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        .sidebar .nav-link.active {
            background-color: #3498db;
            color: #fff;
        }
        .border-left-primary {
            border-left: 0.25rem solid #007bff !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #28a745 !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #17a2b8 !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #ffc107 !important;
        }
        .text-gray-800 {
            color: #5a5c69 !important;
        }
        .text-gray-300 {
            color: #dddfeb !important;
        }
    </style>

    @stack('styles')
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <!-- App Branding -->
                    <div class="text-center mb-4">
                        <!-- App Logo -->
                        <div class="mb-3">
                            <img src="{{ config('app.logo_url') }}"
                                 alt="{{ config('app.name') }} Logo"
                                 class="img-fluid"
                                 style="max-height: {{ config('app.logo_max_height') }}; max-width: {{ config('app.logo_max_width') }};">
                        </div>

                        <!-- App Name -->
                        <div class="mb-2">
                            <h4 class="text-white mb-1">{{ config('app.name') }}</h4>
                            <small class="text-light">Super Admin</small>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.dashboard') ? 'active' : '' }}"
                               href="{{ route('super_admin.dashboard') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.site_analytics.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.site_analytics.index') }}">
                                <i class="fas fa-chart-line me-2"></i>
                                Site Analytics
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.organizations.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.organizations.index') }}">
                                <i class="fas fa-building me-2"></i>
                                Organizations
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.plans.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.plans.index') }}">
                                <i class="fas fa-layer-group me-2"></i>
                                Plans
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.subscriptions.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.subscriptions.index') }}">
                                <i class="fas fa-credit-card me-2"></i>
                                Subscriptions
                            </a>
                        </li>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.payment-accounts.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.payment-accounts.index') }}">
                                <i class="fas fa-university me-2"></i>
                                Payment Accounts
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.currency.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.currency.index') }}">
                                <i class="fas fa-coins me-2"></i>
                                Currency Management
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.subscription-payments.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.subscription-payments.index') }}">
                                <i class="fas fa-money-check-alt me-2"></i>
                                Payment Management
                            </a>
                        </li>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.affiliates.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.affiliates.index') }}">
                                <i class="fas fa-handshake me-2"></i>
                                Affiliate Management
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.affiliate-earnings.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.affiliate-earnings.index') }}">
                                <i class="fas fa-dollar-sign me-2"></i>
                                Affiliate Earnings
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.affiliate-withdrawals.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.affiliate-withdrawals.index') }}">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Affiliate Withdrawals
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.affiliate_settings.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.affiliate_settings.index') }}">
                                <i class="fas fa-cogs me-2"></i>
                                Affiliate Settings
                            </a>
                        </li>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.impersonation.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.impersonation.index') }}">
                                <i class="fas fa-user-secret me-2"></i>
                                User Impersonation
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.system-logs.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.system-logs.index') }}">
                                <i class="fas fa-list-alt me-2"></i>
                                System Logs
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.support.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.support.dashboard') }}">
                                <i class="fas fa-headset me-2"></i>
                                Support Dashboard
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.knowledge-base.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.knowledge-base.index') }}">
                                <i class="fas fa-book me-2"></i>
                                Knowledge Base
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.announcements.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.announcements.index') }}">
                                <i class="fas fa-bullhorn me-2"></i>
                                Announcements
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.welcome-messages.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.welcome-messages.index') }}">
                                <i class="fas fa-envelope-open-text me-2"></i>
                                Welcome Messages
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.email-testing.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.email-testing.index') }}">
                                <i class="fas fa-vial me-2"></i>
                                Email Testing
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.reports.*') ? 'active' : '' }}"
                               href="#" onclick="alert('Reports feature coming soon!')">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                        </li>

                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">

                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('super_admin.profile.*') ? 'active' : '' }}"
                               href="{{ route('super_admin.profile.index') }}">
                                <i class="fas fa-user-cog me-2"></i>
                                Profile Settings
                            </a>
                        </li>

                        <li class="nav-item">
                            <form method="POST" action="{{ route('super_admin.logout') }}" class="d-inline">
                                @csrf
                                <button type="submit" class="nav-link border-0 bg-transparent text-start w-100">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top Navigation -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="d-flex align-items-center">
                        <button class="btn btn-outline-secondary d-md-none me-2" type="button" data-bs-toggle="collapse" data-bs-target=".sidebar">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h1 class="h2 mb-0">@yield('page-title', 'Dashboard')</h1>
                    </div>

                    <div class="d-flex align-items-center">
                        <!-- Currency Override Indicator -->
                        @php
                            $overrideEnabled = \App\Models\CurrencySetting::get('system_currency_override_enabled', false);
                            $overrideCurrency = \App\Models\CurrencySetting::get('system_currency_override', 'USD');
                            $overrideMethod = \App\Models\CurrencySetting::get('override_method', 'direct');
                            $overrideIp = \App\Models\CurrencySetting::get('override_ip_address', '');
                        @endphp

                        @if($overrideEnabled)
                            <div class="me-3">
                                <span class="badge bg-warning text-dark d-flex align-items-center">
                                    <i class="fas fa-globe-americas me-1"></i>
                                    Currency Override: {{ $overrideCurrency }}
                                    @if($overrideMethod === 'ip_simulation' && $overrideIp)
                                        <small class="ms-1">(IP: {{ $overrideIp }})</small>
                                    @endif
                                </span>
                                <div class="small text-muted">
                                    All users see {{ $overrideCurrency }} prices
                                </div>
                            </div>
                        @endif

                        <!-- Current Currency Display -->
                        <div class="me-3 text-center">
                            <div class="small text-muted">Current Currency</div>
                            <span class="badge bg-{{ user_currency() === 'NGN' ? 'success' : 'primary' }}">
                                {{ user_currency_symbol() }} {{ user_currency() }}
                            </span>
                        </div>

                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>
                                {{ auth('super_admin')->user()->name }}
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="{{ route('super_admin.profile.index') }}">
                                        <i class="fas fa-user-cog me-2"></i>
                                        Profile Settings
                                    </a>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('super_admin.logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item">
                                            <i class="fas fa-sign-out-alt me-2"></i>
                                            Logout
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Flash Messages -->
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if($errors->any())
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Please fix the following errors:</strong>
                        <ul class="mb-0 mt-2">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Page Content -->
                @yield('content')
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>

    @stack('scripts')
</body>
</html>
