@extends('layouts.organization-auth')

@section('title', 'Set New Password - Sales Management System')

@section('content')
<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left Side - Branding -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary text-white">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fas fa-shield-alt" style="font-size: 4rem;"></i>
                </div>
                <h2 class="display-4 fw-bold mb-3">Secure Reset</h2>
                <p class="lead mb-4">
                    Create a strong, secure password for your account. Make sure it's something
                    you'll remember but others can't guess.
                </p>
                <div class="row text-center">
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-lock fa-2x"></i>
                        </div>
                        <small>Strong Security</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-user-shield fa-2x"></i>
                        </div>
                        <small>Account Protection</small>
                    </div>
                    <div class="col-4">
                        <div class="mb-2">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                        <small>Verified Access</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Reset Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center auth-form-side">
            <div class="w-100" style="max-width: 400px;">
                <!-- App Logo -->
                <div class="text-center mb-4">
                    <a href="{{ route('landing.home') }}" class="text-decoration-none d-flex justify-content-center">
                        <img src="{{ config('app.auth_logo_url') }}"
                             alt="{{ config('app.name') }} Logo"
                             class="img-fluid mb-3"
                             style="max-height: {{ config('app.auth_logo_max_height') }}; max-width: {{ config('app.auth_logo_max_width') }};">
                    </a>
                    <h3 class="fw-bold text-dark">Set New Password</h3>
                    <p class="text-muted">Create a secure password for your account</p>
                </div>

                @if ($errors->any())
                <div class="alert alert-danger border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <div>
                            <strong>Please fix the following errors:</strong>
                            <ul class="mb-0 mt-2">
                                @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
                @endif

                <form method="POST" action="{{ route('password.store') }}">
                    @csrf

                    <!-- Password Reset Token -->
                    <input type="hidden" name="token" value="{{ $request->route('token') }}">

                    <!-- Email Address -->
                    <div class="form-floating mb-4">
                        <input type="email"
                               class="form-control @error('email') is-invalid @enderror"
                               id="email"
                               name="email"
                               value="{{ old('email', $request->email) }}"
                               required
                               autofocus
                               autocomplete="username"
                               placeholder="Enter your email address">
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>Email Address
                        </label>
                        @error('email')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- New Password -->
                    <div class="form-floating mb-4 position-relative">
                        <input type="password"
                               class="form-control @error('password') is-invalid @enderror"
                               id="password"
                               name="password"
                               required
                               autocomplete="new-password"
                               placeholder="Enter new password">
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>New Password
                        </label>
                        <button type="button" class="btn btn-link position-absolute top-50 end-0 translate-middle-y me-3 text-muted"
                                onclick="togglePassword('password')" style="z-index: 10; border: none; background: none;">
                            <i class="fas fa-eye" id="password-eye"></i>
                        </button>
                        @error('password')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Confirm Password -->
                    <div class="form-floating mb-4 position-relative">
                        <input type="password"
                               class="form-control @error('password_confirmation') is-invalid @enderror"
                               id="password_confirmation"
                               name="password_confirmation"
                               required
                               autocomplete="new-password"
                               placeholder="Confirm new password">
                        <label for="password_confirmation">
                            <i class="fas fa-check-circle me-2"></i>Confirm Password
                        </label>
                        <button type="button" class="btn btn-link position-absolute top-50 end-0 translate-middle-y me-3 text-muted"
                                onclick="togglePassword('password_confirmation')" style="z-index: 10; border: none; background: none;">
                            <i class="fas fa-eye" id="password_confirmation-eye"></i>
                        </button>
                        @error('password_confirmation')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Password Requirements -->
                    <div class="alert alert-info border-0 rounded-3 mb-4">
                        <h6 class="mb-2"><i class="fas fa-info-circle me-2"></i>Password Requirements:</h6>
                        <ul class="small mb-0">
                            <li>At least 8 characters long</li>
                            <li>Contains uppercase and lowercase letters</li>
                            <li>Includes at least one number</li>
                            <li>Contains at least one special character</li>
                        </ul>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid mb-4">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>
                            Reset Password
                        </button>
                    </div>

                    <div class="text-center">
                        <a href="{{ route('organization.login') }}" class="text-decoration-none">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to organization login
                        </a>
                    </div>

                </form>

                <!-- Security Tips -->
                <div class="text-center mt-4 pt-4 border-top">
                    <h6 class="text-muted mb-3">Security Tips</h6>
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="mb-2">
                                <i class="fas fa-eye-slash text-muted"></i>
                            </div>
                            <small class="text-muted">
                                Don't share your password
                            </small>
                        </div>
                        <div class="col-6">
                            <div class="mb-2">
                                <i class="fas fa-sync-alt text-muted"></i>
                            </div>
                            <small class="text-muted">
                                Change it regularly
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const eyeIcon = document.getElementById(fieldId + '-eye');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}
</script>
@endsection
